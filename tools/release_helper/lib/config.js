const fs = require('fs-extra');
const path = require('path');

class ConfigManager {
  constructor(configPath = '../../deploy.config.json', logger = null) {
    this.configPath = path.resolve(__dirname, configPath);
    this.logger = logger;
    this.config = null;
  }

  async loadConfig() {
    try {
      this.logger?.debug(`Loading configuration from: ${this.configPath}`);
      
      if (!await fs.pathExists(this.configPath)) {
        throw new Error(`Configuration file not found: ${this.configPath}`);
      }

      const configData = await fs.readJson(this.configPath);
      this.config = configData;
      
      this.logger?.debug('Configuration loaded successfully');
      this.validateConfig();
      
      return this.config;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Configuration file not found: ${this.configPath}`);
      } else if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON in configuration file: ${error.message}`);
      }
      throw error;
    }
  }

  validateConfig() {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call loadConfig() first.');
    }

    this.logger?.debug('Validating configuration structure');

    // Check for required top-level environments
    const requiredEnvs = ['prod', 'demo'];
    for (const env of requiredEnvs) {
      if (!this.config[env]) {
        throw new Error(`Missing required environment configuration: ${env}`);
      }

      // Validate environment structure
      this.validateEnvironmentConfig(env, this.config[env]);
    }

    this.logger?.debug('Configuration validation completed');
  }

  validateEnvironmentConfig(envName, envConfig) {
    // Check for required appId
    if (!envConfig.appId) {
      throw new Error(`Missing appId for environment: ${envName}`);
    }

    if (typeof envConfig.appId !== 'number' && typeof envConfig.appId !== 'string') {
      throw new Error(`Invalid appId type for environment ${envName}. Must be number or string.`);
    }

    // Check for required depots
    if (!envConfig.depots) {
      throw new Error(`Missing depots configuration for environment: ${envName}`);
    }

    // Check for required depot platforms
    const requiredPlatforms = ['windows', 'linux', 'mac'];
    for (const platform of requiredPlatforms) {
      if (!envConfig.depots[platform]) {
        throw new Error(`Missing depot ID for platform ${platform} in environment: ${envName}`);
      }

      if (typeof envConfig.depots[platform] !== 'number' && typeof envConfig.depots[platform] !== 'string') {
        throw new Error(`Invalid depot ID type for ${platform} in environment ${envName}. Must be number or string.`);
      }
    }
  }

  getEnvironmentConfig(env) {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call loadConfig() first.');
    }

    if (!['prod', 'demo', 'dev'].includes(env)) {
      throw new Error(`Invalid environment: ${env}. Must be 'dev', 'prod' or 'demo'.`);
    }

    const envConfig = this.config[env];
    if (!envConfig) {
      throw new Error(`Environment configuration not found: ${env}`);
    }

    this.logger?.debug(`Retrieved configuration for environment: ${env}`);
    return envConfig;
  }

  getAppId(env) {
    const envConfig = this.getEnvironmentConfig(env);
    return envConfig.appId;
  }

  getDepotId(env, platform) {
    const envConfig = this.getEnvironmentConfig(env);
    
    if (!['windows', 'linux', 'mac'].includes(platform)) {
      throw new Error(`Invalid platform: ${platform}. Must be 'windows', 'linux', or 'mac'.`);
    }

    const depotId = envConfig.depots[platform];
    if (!depotId) {
      throw new Error(`Depot ID not found for platform ${platform} in environment ${env}`);
    }

    return depotId;
  }

  getAllDepots(env) {
    const envConfig = this.getEnvironmentConfig(env);
    return envConfig.depots;
  }

  // Helper method to create a sample configuration file
  static async createSampleConfig(configPath) {
    const sampleConfig = {
      "prod": {
        "appId": 3391210,
        "depots": {
          "windows": 3391212,
          "linux": 3391211,
          "mac": 3391213
        }
      },
      "demo": {
        "appId": 3612920,
        "depots": {
          "windows": 3612922,
          "linux": 3612921,
          "mac": 3612923
        }
      }
    };

    await fs.writeJson(configPath, sampleConfig, { spaces: 2 });
    return sampleConfig;
  }

  // Get configuration summary for logging
  getConfigSummary() {
    if (!this.config) {
      return 'Configuration not loaded';
    }

    const summary = {};
    for (const [env, config] of Object.entries(this.config)) {
      summary[env] = {
        appId: config.appId,
        depots: Object.keys(config.depots || {}).length
      };
    }

    return summary;
  }
}

module.exports = ConfigManager;
