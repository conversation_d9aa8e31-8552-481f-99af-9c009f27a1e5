#!/usr/bin/env node

const { Command } = require('commander');
const path = require('path');
const Logger = require('./lib/logger');
const ConfigManager = require('./lib/config');
const EnvironmentManager = require('./lib/environment');
const VersionManager = require('./lib/version');
const BuildManager = require('./lib/build');
const SteamUploader = require('./lib/steam');

// Initialize logger
const logger = new Logger(path.join(__dirname, 'deploy.log'));

const program = new Command();

program
  .name('deploy')
  .description('Deployment script for Tryu Sora Space Shooter - automated versioning, building, and Steam deployment')
  .version('1.0.0');

// Version bumping
program
  .option('--bump-version <type>', 'Bump version segment (major, minor, patch)', (value) => {
    if (!['major', 'minor', 'patch'].includes(value)) {
      logger.error(`Invalid version bump type: ${value}. Must be one of: major, minor, patch`);
      process.exit(1);
    }
    return value;
  });

// Environment switching
program
  .option('--checkout-env <env>', 'Switch to environment (prod, demo)', (value) => {
    if (!['prod', 'demo','dev'].includes(value)) {
      logger.error(`Invalid environment: ${value}. Must be one of: dev, prod, demo`);
      process.exit(1);
    }
    return value;
  });

// Build options
program
  .option('--build-all', 'Build for all platforms (Windows, Linux, macOS)')
  .option('--build-windows', 'Build for Windows only')
  .option('--build-linux', 'Build for Linux only')
  .option('--build-mac', 'Build for macOS only');

// Steam upload
program
  .option('--upload', 'Upload builds to Steam')
  .option('--content-desc <description>', 'Content description for Steam upload');

// Debug options
program
  .option('--verbose', 'Enable verbose logging')
  .option('--dry-run', 'Show what would be done without executing');

program.parse();

const options = program.opts();

// Set logging level
if (options.verbose) {
  logger.setLevel('DEBUG');
}

// Validate option combinations
function validateOptions() {
  logger.debug('Validating command-line options');

  // Check if any build option is specified
  const buildOptions = [options.buildAll, options.buildWindows, options.buildLinux, options.buildMac];
  const hasBuildOption = buildOptions.some(option => option);

  // If upload is specified, ensure environment is set
  if (options.upload && !options.checkoutEnv) {
    logger.error('--upload requires --checkout-env to be specified');
    process.exit(1);
  }

  // If upload is specified, ensure at least one build option is set or builds exist
  if (options.upload && !hasBuildOption) {
    logger.warn('--upload specified without build options. Assuming builds already exist.');
  }

  // If content description is provided without upload, warn user
  if (options.contentDesc && !options.upload) {
    logger.warn('--content-desc specified without --upload. Description will be ignored.');
  }

  logger.debug('Options validation completed');
}

async function main() {
  try {
    logger.info('Starting Tryu deployment script');
    logger.separator();

    // Validate options
    validateOptions();

    // Show what will be executed
    if (options.dryRun) {
      logger.info('DRY RUN MODE - No changes will be made');
      logger.separator();
    }

    // Log the selected options
    logger.debug(`Selected options: ${JSON.stringify(options, null, 2)}`);

    // Initialize managers
    const configManager = new ConfigManager('../../../deploy.config.json', logger);
    const environmentManager = new EnvironmentManager(logger);
    const versionManager = new VersionManager(null, logger);
    const buildManager = new BuildManager(logger);
    const steamUploader = new SteamUploader(configManager, logger);

    // Load configuration
    await configManager.loadConfig();
    logger.debug(`Configuration loaded: ${JSON.stringify(configManager.getConfigSummary())}`);

    // Execute operations based on options
    if (options.bumpVersion) {
      if (options.dryRun) {
        const currentVersion = await versionManager.getCurrentVersion();
        const newVersion = versionManager.previewBump(options.bumpVersion);
        logger.info(`Would bump version from ${currentVersion} to ${newVersion}`);
      } else {
        await versionManager.bumpVersion(options.bumpVersion);
      }
    }

    if (options.checkoutEnv) {
      if (options.dryRun) {
        logger.info(`Would switch environment to: ${options.checkoutEnv}`);
      } else {
        await environmentManager.switchEnvironment(options.checkoutEnv);
      }
    }

    if (options.buildAll || options.buildWindows || options.buildLinux || options.buildMac) {
      if (options.dryRun) {
        logger.info('Would build project for selected platforms');
        if (options.buildAll) logger.info('  - All platforms (Windows, Linux, macOS)');
        if (options.buildWindows) logger.info('  - Windows');
        if (options.buildLinux) logger.info('  - Linux');
        if (options.buildMac) logger.info('  - macOS');
      } else {
        // Check Godot availability first
        try {
          await buildManager.checkGodotAvailability();
        } catch (error) {
          logger.error(`Godot not available: ${error.message}`);
          logger.error('Please install Godot and ensure it\'s available in your PATH');
          process.exit(1);
        }

        // Execute builds
        if (options.buildAll) {
          await buildManager.buildAll();
        } else {
          if (options.buildWindows) await buildManager.buildPlatform('windows');
          if (options.buildLinux) await buildManager.buildPlatform('linux');
          if (options.buildMac) await buildManager.buildPlatform('mac');
        }
      }
    }

    if (options.upload) {
      if (!options.checkoutEnv) {
        logger.error('--upload requires --checkout-env to be specified');
        process.exit(1);
      }

      if (options.dryRun) {
        logger.info(`Would upload to Steam (${options.checkoutEnv} environment)`);
        if (options.contentDesc) {
          logger.info(`Content description: ${options.contentDesc}`);
        }
      } else {
        // Check SteamCMD availability first
        try {
          await steamUploader.checkSteamCMDAvailability();
        } catch (error) {
          logger.error(`SteamCMD not available: ${error.message}`);
          logger.error('Please install SteamCMD and ensure it\'s available in your PATH');
          logger.info('Download SteamCMD from: https://developer.valvesoftware.com/wiki/SteamCMD');
          process.exit(1);
        }

        // Perform upload
        await steamUploader.upload(options.checkoutEnv, options.contentDesc);
      }
    }

    logger.separator();
    logger.success('Deployment script completed successfully');

  } catch (error) {
    logger.error(`Deployment failed: ${error.message}`);
    logger.debug(`Stack trace: ${error.stack}`);
    process.exit(1);
  } finally {
    logger.sessionEnd();
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error(`Uncaught exception: ${error.message}`);
  logger.debug(`Stack trace: ${error.stack}`);
  logger.sessionEnd();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error(`Unhandled rejection at: ${promise}, reason: ${reason}`);
  logger.sessionEnd();
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main();
}

module.exports = { main, validateOptions };
